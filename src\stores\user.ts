import { defineStore } from 'pinia'
import { computed, ref, watch } from 'vue'

import { useStorage } from '@vueuse/core'

import type { Equipment, Organizer, Player, User } from '../api/feathers-client'
import { api } from '../api/feathers-client'
import { useAuthStore } from './auth'

// Extended player type that includes equipment
interface PlayerWithEquipment extends Player {
  equipment?: Equipment[]
}

// Extended user profile type that includes related data from user-me service
interface UserProfile extends User {
  players?: PlayerWithEquipment[]
  organizers?: Organizer[]
}

/**
 * User store for storing user profile and related data from the user-me service.
 * All related api persistence should be done by dedicated services (player, equipment)
 *
 * This store automatically fetches the user profile when the user is authenticated
 * and clears it when the user logs out.
 *
 * Usage:
 * ```ts
 * const userStore = useUserStore()
 *
 * // Access user profile
 * console.log(userStore.userProfile)
 *
 * // Update user profile
 * await userStore.updateUserProfile({ avatar: 'new-avatar-url' })
 *
 * // Manually fetch profile
 * await userStore.fetchUserProfile()
 * ```
 */

export const useUserStore = defineStore('user', () => {
  // Use the typed service from the api client
  const userMeService = api.userMe
  const userService = api.users

  const userProfile = ref<UserProfile | null>(null)
  const isLoading = ref(false)
  const error = ref<Error | null>(null)

  // Persisted state for active player and organizer
  // Use our own localStorage implementation for better reliability
  const getActivePlayerIdFromStorage = (): number | null => {
    try {
      const stored = localStorage.getItem('activePlayerId')
      return stored ? parseInt(stored, 10) : null
    } catch {
      return null
    }
  }

  const setActivePlayerIdToStorage = (id: number | null): void => {
    try {
      if (id === null) {
        localStorage.removeItem('activePlayerId')
      } else {
        localStorage.setItem('activePlayerId', id.toString())
      }
    } catch {
      // Ignore localStorage errors
    }
  }

  const activePlayerId = ref<number | null>(getActivePlayerIdFromStorage())
  const activeOrganizerId = useStorage<number | null>('activeOrganizerId', null)
  const playerEquipmentSelection = useStorage<Record<number, number>>('playerEquipmentSelection', {})

  // Watch activePlayerId changes and persist to localStorage
  watch(activePlayerId, (newValue) => {
    console.log('👀 activePlayerId changed to:', newValue)
    setActivePlayerIdToStorage(newValue)
  })

  // Computed properties
  const hasProfile = computed(() => !!userProfile.value)
  const fullName = computed(() => {
    if (!userProfile.value) return ''

    // Try to get name from the first player profile
    const firstPlayer = userProfile.value.players?.[0]
    if (firstPlayer?.firstname || firstPlayer?.lastname) {
      const firstName = firstPlayer.firstname || ''
      const lastName = firstPlayer.lastname || ''
      return `${firstName} ${lastName}`.trim()
    }

    // Fallback to email
    return userProfile.value.email
  })

  const activePlayer = computed(() => {
    if (!userProfile.value?.players || userProfile.value.players.length === 0) return null

    console.log('🔍 activePlayer computed - activePlayerId from localStorage:', activePlayerId.value)
    console.log('🔍 activePlayer computed - available players:', userProfile.value.players.map(p => ({ id: p.id, name: `${p.firstname} ${p.lastname}` })))

    // Get the player from localStorage or default to first player
    let player: PlayerWithEquipment | null = null
    if (activePlayerId.value) {
      player = userProfile.value.players.find((p: PlayerWithEquipment) => p.id === activePlayerId.value) || null
      console.log('🔍 activePlayer computed - found player by ID:', player ? `${player.firstname} ${player.lastname} (${player.id})` : 'not found')
    }

    // If no player found or no activePlayerId set, use first player BUT don't automatically set activePlayerId
    if (!player) {
      player = userProfile.value.players[0]
      console.log('🔍 activePlayer computed - falling back to first player:', `${player.firstname} ${player.lastname} (${player.id})`)

      // Only set activePlayerId if it's explicitly null (not just temporarily undefined during loading)
      if (activePlayerId.value === null && userProfile.value.players.length > 0) {
        console.log('🔍 activePlayer computed - setting activePlayerId to first player')
        activePlayerId.value = player.id
      }
    }

    if (player) {
      // Get equipment selection for this player
      const activeEquipmentId = playerEquipmentSelection.value[player.id]
      const equipment = player.equipment || []
      const activeEquipment = equipment.find((e: Equipment) => e.id === activeEquipmentId) || null

      return {
        ...player,
        activeEquipmentId,
        activeEquipment,
        equipment,
      }
    }

    return player
  })

  const activeOrganizer = computed(() => {
    const organizers = userProfile.value?.organizers
    if (!organizers || !Array.isArray(organizers) || organizers.length === 0) {
      return null
    }

    if (!activeOrganizerId.value) {
      return null
    }

    return organizers.find((o: Organizer) => o?.id === activeOrganizerId.value) || null
  })

  const playerLocation = computed(() => {
    const player = activePlayer.value
    if (!player) return null

    const parts = [player.city, player.country].filter(Boolean)
    return parts.length > 0 ? parts.join(', ') : null
  })


  // Actions
  // Actions
  function setActivePlayer(playerId: number) {
    console.log('🎯 setActivePlayer called with playerId:', playerId)
    if (!playerId || typeof playerId !== 'number') {
      console.log('🎯 setActivePlayer - invalid playerId provided')
      return
    }
    const players = userProfile.value?.players
    if (!players || !Array.isArray(players)) {
      console.log('🎯 setActivePlayer - no players available')
      return
    }
    const playerExists = players.some((p: PlayerWithEquipment) => p?.id === playerId)
    console.log('🎯 setActivePlayer - player exists:', playerExists)
    if (playerExists) {
      console.log('🎯 setActivePlayer - setting activePlayerId from', activePlayerId.value, 'to', playerId)
      activePlayerId.value = playerId
      // Ensure only one is selected at a time
      activeOrganizerId.value = null
    }
  }

  function setActiveOrganizer(organizerId: number) {
    if (!organizerId || typeof organizerId !== 'number') {
      return
    }
    const organizers = userProfile.value?.organizers
    if (!organizers || !Array.isArray(organizers)) {
      return
    }
    const organizerExists = organizers.some((o: Organizer) => o?.id === organizerId)
    if (organizerExists) {
      activeOrganizerId.value = organizerId
      // Ensure only one is selected at a time
      activePlayerId.value = null
    }
  }

  async function setActiveEquipment(equipment: Equipment) {
    const player = activePlayer.value
    if (!player?.id) {
      error.value = new Error('No active player selected')
      return
    }
    if (!equipment?.id) {
      error.value = new Error('Invalid equipment provided')
      return
    }
    playerEquipmentSelection.value[player.id] = equipment.id
  }

  async function fetchUserProfile() {
    const authStore = useAuthStore()

    if (!authStore.isAuthenticated) {
      error.value = new Error('User not authenticated')
      return
    }

    // Check localStorage directly to debug the issue
    const localStorageValue = localStorage.getItem('activePlayerId')
    console.log('🔄 fetchUserProfile - Direct localStorage check:', localStorageValue)
    console.log('🔄 fetchUserProfile - Starting fetch, current activePlayerId:', activePlayerId.value)

    // Small delay to ensure localStorage is fully loaded
    await new Promise(resolve => setTimeout(resolve, 50))
    console.log('🔄 fetchUserProfile - After delay, activePlayerId:', activePlayerId.value)

    isLoading.value = true
    error.value = null

    try {
      // user-me is a singleton service, find returns the single record.
      const profile = await userMeService.find({})
      userProfile.value = profile as UserProfile

      console.log('🔄 fetchUserProfile - Profile fetched, players:', userProfile.value?.players?.map(p => ({ id: p.id, name: `${p.firstname} ${p.lastname}` })))
      console.log('🔄 fetchUserProfile - Before setting defaults, activePlayerId:', activePlayerId.value)

      // Set default active player ONLY if both are not set and players exist
      if (!activePlayerId.value && !activeOrganizerId.value && userProfile.value?.players && userProfile.value.players.length > 0) {
        console.log('🔄 fetchUserProfile - Neither activePlayerId nor activeOrganizerId set, setting to first player:', userProfile.value.players[0].id)
        activePlayerId.value = userProfile.value.players[0].id
      }
      // Do NOT auto-set organizer if both are unset

      // Validate that the current activePlayerId still exists in the fetched players
      if (activePlayerId.value && userProfile.value?.players) {
        const playerExists = userProfile.value.players.some(p => p.id === activePlayerId.value)
        console.log('🔄 fetchUserProfile - Validating activePlayerId exists:', activePlayerId.value, 'exists:', playerExists)
        if (!playerExists && userProfile.value.players.length > 0) {
          // If the selected player no longer exists, fall back to first player
          console.log('🔄 fetchUserProfile - Player no longer exists, falling back to first player:', userProfile.value.players[0].id)
          activePlayerId.value = userProfile.value.players[0].id
        }
      }

      console.log('🔄 fetchUserProfile - Final activePlayerId:', activePlayerId.value)
    } catch (err) {
      if (err instanceof Error) {
        error.value = err
      } else {
        error.value = new Error('Failed to fetch user profile')
      }
      userProfile.value = null
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function updateUserProfile(data: Partial<User>) {
    const authStore = useAuthStore()

    if (!authStore.isAuthenticated) {
      error.value = new Error('User not authenticated')
      return
    }

    isLoading.value = true
    error.value = null

    try {
      if (!userProfile.value?.id) throw new Error('User ID not found')
      const updatedProfile = await userService.patch(userProfile.value.id, data, {})
      // Merge the updated data with the current profile
      if (userProfile.value) {
        userProfile.value = { ...userProfile.value, ...updatedProfile }
      }
      return updatedProfile
    } catch (err) {
      if (err instanceof Error) {
        error.value = err
      } else {
        error.value = new Error('Failed to update user profile')
      }
      throw err
    } finally {
      isLoading.value = false
    }
  }

  function clearUserProfile() {
    console.log('🧹 clearUserProfile called - clearing userProfile but preserving activePlayerId:', activePlayerId.value)
    userProfile.value = null
    // Do NOT clear activePlayerId - it should persist across sessions
    // activePlayerId.value = null
    activeOrganizerId.value = null
    // Do not clear playerEquipmentSelection, as it should persist for the user
    error.value = null
  }

  // Watch for authentication changes and auto-fetch profile
  const authStore = useAuthStore()

  // Auto-fetch profile when user becomes authenticated
  watch(
    () => authStore.isAuthenticated,
    (isAuthenticated) => {
      console.log('🔐 Auth status changed to:', isAuthenticated, 'userProfile exists:', !!userProfile.value)
      if (isAuthenticated && !userProfile.value) {
        console.log('🔐 User authenticated and no profile, fetching profile...')
        fetchUserProfile()
      } else if (!isAuthenticated) {
        console.log('🔐 User not authenticated, clearing profile...')
        clearUserProfile()
      } else {
        console.log('🔐 Auth status changed but no action needed')
      }
    },
    { immediate: true }
  )

  return {
    userProfile,
    isLoading,
    error,
    hasProfile,
    fullName,
    activePlayer,
    activeOrganizer,
    playerLocation,
    fetchUserProfile,
    updateUserProfile,
    clearUserProfile,
    setActivePlayer,
    setActiveOrganizer,
    setActiveEquipment,
  }
})
